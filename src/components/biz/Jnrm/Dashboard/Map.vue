<script setup lang="ts">
import { useTransportOrderStore, type TransportOrder } from '~/stores/useTransportOrderStore'
import { FactoryType } from '~/types/biz/biztypes'
import VehicleLayer from './VehicleLayer.vue'
import VehicleInfoPopup from './VehicleInfoPopup.vue'

// 百度地图相关变量
let BMapGL: any
let map: any
let isMapLoaded = ref(false)

// 使用运单store
const orderStore = useTransportOrderStore()
// 使用地点store
const locationStore = useLocationStore()

// 全局电厂选择
const plantStore = usePlantStore()
const { selectedDeptId } = storeToRefs(plantStore)

// 工具：获取当前选中电厂对应的地点（通过 locationId 映射）
function getSelectedLocation(): any | null {
	try {
		const plants = (plantStore.list as any[]) || []
		const locations = (locationStore.list as any[]) || []
		const plant = plants.find((p: any) => p?.id === selectedDeptId.value)
		if (!plant?.locationId) return null
		return locations.find((loc: any) => loc?.id === plant.locationId) || null
	} catch (e) {
		return null
	}
}

// 地图配置
const mapConfig = reactive({
	center: { lng: 112.549248, lat: 37.857014 }, // 太原市中心
	zoom: 8,
})

// 图层可见性控制
const layerVisible = reactive({
	roadNetwork: true,
	powerPlant: true,
	coalMine: true,
	vehicles: true,
} as Record<string, boolean>)

// 运单标记映射表
const orderMarkers = new Map<string, any>()

// 电厂和煤矿标记
let powerPlantMarkers: any[] = []
let coalMineMarkers: any[] = []
let normalMarkers: any[] = []
let warningMarkers: any[] = []
let dangerMarkers: any[] = []
let trackPolylines: any[] = []

// 当前显示的轨迹线
let currentTrackPolyline: any = null

// 当前选中的运单ID
const selectedOrderId = ref<string | null>(null)

/**
 * 绘制车辆轨迹
 * @param order 选中的运单
 */
const drawVehicleTrack = (order: TransportOrder) => {
	// 清除之前的轨迹线
	if (currentTrackPolyline) {
		map.removeOverlay(currentTrackPolyline)
		currentTrackPolyline = null
	}

	// 检查边界条件：historyPositions为空或不存在
	if (!order.historyPositions || order.historyPositions.length === 0) {
		console.log('运单轨迹数据为空，无法绘制轨迹')
		return
	}

	// 将历史位置转换为百度地图点数组
	const points = order.historyPositions
		.filter((pos: any) => pos.lng && pos.lat) // 过滤无效坐标
		.map((pos: any) => new BMapGL.Point(pos.lng, pos.lat))

	// 如果有当前位置，也加入轨迹
	if (order.currentPosition && order.currentPosition.lng && order.currentPosition.lat) {
		points.push(new BMapGL.Point(order.currentPosition.lng, order.currentPosition.lat))
	}

	// 检查是否有足够的点来绘制轨迹
	if (points.length < 2) {
		console.log('轨迹点数量不足，无法绘制轨迹')
		return
	}

	// 创建轨迹线
	currentTrackPolyline = new BMapGL.Polyline(points, {
		strokeTexture: {
			url: 'https://mapopen-pub-jsapigl.bj.bcebos.com/svgmodel/Icon_road_blue_arrow.png',
			width: 16,
			height: 64,
		},
		strokeWeight: 8,
		strokeOpacity: 0.8,
	})

	// 添加轨迹线到地图
	map.addOverlay(currentTrackPolyline)

	console.log(`已绘制运单 ${order.orderNo} 的轨迹，包含 ${points.length} 个点`)
}

// 监听选中运单变化，自动定位到地图中心并绘制轨迹
watch(
	() => orderStore.selectedOrder,
	(newOrder) => {
		if (newOrder && newOrder.currentPosition && map) {
			const point = new BMapGL.Point(newOrder.currentPosition.lng, newOrder.currentPosition.lat)
			map.centerAndZoom(point, 12)
			// 绘制车辆轨迹
			drawVehicleTrack(newOrder)
		} else {
			// 如果没有选中运单，清除轨迹线
			if (currentTrackPolyline && map) {
				map.removeOverlay(currentTrackPolyline)
				currentTrackPolyline = null
			}
		}
	},
)

// 监听运单列表变化，更新地图标记
watch(
	() => orderStore.orderList,
	() => {
		if (!isMapLoaded.value) return
		refreshOrdersOnMap()
	},
	{ deep: true },
)

// 监听电厂选择变化，刷新当前地图标记并定位到电厂/默认位置
watch(selectedDeptId, async () => {
	if (!isMapLoaded.value) return
	// 重置地图上的运单标记
	orderMarkers.forEach((marker) => map.removeOverlay(marker))
	orderMarkers.clear()
	// 优先：根据电厂的 locationId 获取经纬度居中；若无选中电厂则回到默认位置
	const loc = getSelectedLocation()
	console.log('loc---', loc)
	if (loc && typeof loc.longitude === 'number' && typeof loc.latitude === 'number') {
		const p = new BMapGL.Point(loc.longitude, loc.latitude)
		map.centerAndZoom(p, 12)
	} else {
		const p0 = new BMapGL.Point(mapConfig.center.lng, mapConfig.center.lat)
		map.centerAndZoom(p0, mapConfig.zoom)
	}
	// 之后刷新过滤后的运单标记
	refreshOrdersOnMap()
})

// 根据电厂选择过滤运单并刷新标记
function refreshOrdersOnMap() {
	const base = orderStore.orderList
	const filtered = selectedDeptId.value ? base.filter((o: any) => o.deptId === selectedDeptId.value) : base
	updateOrderMarkers(filtered)
}

/**
 * 加载百度地图API
 * @returns Promise<void> 加载完成的Promise
 */
const loadBaiduMapAPI = (): Promise<void> => {
	return new Promise((resolve, reject) => {
		// 如果百度地图API已经加载，直接返回
		if ((window as any).BMapGL) {
			BMapGL = (window as any).BMapGL
			resolve()
			return
		}

		// 设置全局回调函数
		;(window as any).initBaiduMapCallback = () => {
			BMapGL = (window as any).BMapGL
			console.log('地图js加载完成', BMapGL)
			resolve()
		}

		// 创建script标签加载百度地图API
		const script = document.createElement('script')
		script.src = `https://api.map.baidu.com/api?v=1.0&type=webgl&ak=${import.meta.env.VITE_BD_MAP_KEY}&callback=initBaiduMapCallback`
		script.onerror = reject
		document.head.appendChild(script)
	})
}

// 初始化地图
const initMap = async () => {
	try {
		// 创建地图实例
		map = new BMapGL.Map('map')
		console.log(BMapGL)
		// 设置地图中心点和缩放级别
		const point = new BMapGL.Point(mapConfig.center.lng, mapConfig.center.lat)
		map.centerAndZoom(point, mapConfig.zoom)

		// 启用地图功能
		map.enableScrollWheelZoom(true)
		map.enableDragging(true)
		map.enableDoubleClickZoom(true)

		// 设置地图主题 json
		// map.setMapStyleV2({
		// 	styleJson: import('./custom_map_dark.json'),
		// })

		// 设置地图主题 id
		map.setMapStyleV2({
			styleId: 'd19756ff2da97518d11b507292412731',
		})

		// 添加电厂和煤矿标记
		await addPowerPlantMarkers()
		await addCoalMineMarkers()

		// 标记地图已加载
		isMapLoaded.value = true

		// 初始化WebSocket连接
		orderStore.initWebSocket()

		console.log('百度地图初始化完成')
	} catch (error) {
		console.error('地图初始化失败:', error)
	}
}

// 创建车辆图标
const createCarIcon = (type: string) => {
	return new BMapGL.Icon('/truck.png', new BMapGL.Size(type === 'normal' ? 40 : 50, type === 'normal' ? 40 : 50), {
		anchor: new BMapGL.Size(type === 'normal' ? 20 : 25, type === 'normal' ? 20 : 25),
	})
}

/**
 * 添加电厂标记
 * 在地图上添加电厂位置标记，使用Label显示名称
 */
const addPowerPlantMarkers = async () => {
	if (!layerVisible.powerPlant) return

	console.log('addPowerPlantMarkers 开始执行')
	const powerPlantIcon = new BMapGL.Icon('/factory.png', new BMapGL.Size(50, 50), { anchor: new BMapGL.Size(25, 25) })

	try {
		console.log('准备获取电厂数据...')
		// 从 store 获取电厂数据
		const powerPlants = await locationStore.getListByFactoryType(FactoryType.电厂)
		console.log('powerPlants---', powerPlants)

		powerPlants.forEach((plant) => {
			// 如果没有经纬度信息，跳过
			if (!plant.longitude || !plant.latitude) return

			const point = new BMapGL.Point(plant.longitude, plant.latitude)
			const marker = new BMapGL.Marker(point, { icon: powerPlantIcon })
			map.addOverlay(marker)
			powerPlantMarkers.push(marker)

			// 添加标签显示电厂名称
			const label = new BMapGL.Label(
				`<div style="text-align: center;">
					<div style="font-size: 14px; font-weight: bold;">${plant.name}</div>
					<div style="font-size: 12px; margin-top: 2px; color: #666;">⚡ 电厂</div>
				</div>`,
				{
					offset: new BMapGL.Size(-40, -80),
				},
			)

			label.setStyle({
				color: 'black',
				fontSize: '10px',
				background: 'rgba(255, 215, 0, 0.9)',
				border: 'none',
				borderRadius: '5px',
				padding: '4px 8px',
				whiteSpace: 'nowrap',
			})
			marker.setLabel(label)
		})
	} catch (error) {
		console.error('获取电厂数据失败:', error)
		return
	}
}

/**
 * 添加煤矿标记
 * 在地图上添加煤矿位置标记，使用Label显示名称
 */
const addCoalMineMarkers = async () => {
	if (!layerVisible.coalMine) return

	console.log('addCoalMineMarkers 开始执行')
	const coalMineIcon = new BMapGL.Icon('/coal.png', new BMapGL.Size(50, 50), { anchor: new BMapGL.Size(25, 25) })

	try {
		console.log('准备获取煤场数据...')
		// 从 store 获取煤场数据
		const coalMines = await locationStore.getListByFactoryType(FactoryType.煤场)
		console.log('coalMines---', coalMines)
		coalMines.forEach((mine) => {
			// 如果没有经纬度信息，跳过
			if (!mine.longitude || !mine.latitude) return

			const point = new BMapGL.Point(mine.longitude, mine.latitude)
			const marker = new BMapGL.Marker(point, { icon: coalMineIcon })
			map.addOverlay(marker)
			coalMineMarkers.push(marker)

			// 添加标签显示煤矿名称
			const label = new BMapGL.Label(
				`<div style="text-align: center;">
				<div style="font-size: 14px; font-weight: bold;">${mine.name}</div>
				<div style="font-size: 12px; margin-top: 2px; color: #888;">煤矿</div>
			</div>`,
				{
					offset: new BMapGL.Size(-40, -80),
				},
			)

			label.setStyle({
				color: 'white',
				fontSize: '10px',
				background: 'rgba(0, 0, 0, 0.8)',
				border: 'none',
				borderRadius: '5px',
				padding: '4px 8px',
				whiteSpace: 'nowrap',
			})
			marker.setLabel(label)
		})
	} catch (error) {
		console.error('获取煤场数据失败:', error)
		return
	}
}

// 更新运单标记
const updateOrderMarkers = (orderList: TransportOrder[]) => {
	if (!map || !layerVisible.vehicles) return

	// 移除不存在的运单标记
	for (const [orderId, marker] of orderMarkers.entries()) {
		const orderExists = orderList.some((order) => order.id === orderId)
		if (!orderExists) {
			map.removeOverlay(marker) // 地图删掉
			orderMarkers.delete(orderId) // 数据删掉
		}
	}

	// 添加或更新运单标记
	orderList.forEach((order) => {
		if (order.currentPosition) {
			const { lng, lat } = order.currentPosition
			const orderId = order.id

			// 获取运单状态颜色
			const color = orderStore.getOrderStatusColor(order)

			// 创建或更新标记
			if (orderMarkers.has(orderId)) {
				// 更新现有标记位置
				const marker = orderMarkers.get(orderId)
				const newPoint = new BMapGL.Point(lng, lat)
				marker.setPosition(newPoint)
			} else {
				// 创建新标记
				const point = new BMapGL.Point(lng, lat)
				const icon = createCarIcon(color)
				const marker = new BMapGL.Marker(point, { icon })

				map.addOverlay(marker)

				// 添加点击事件
				marker.addEventListener('click', () => {
					showOrderDetails(orderId)
				})

				// 添加标签
				const label = new BMapGL.Label(
					`<div style="text-align: center;">
						<div style="font-size: 16px;">${order.orderNo}</div>
						<div style="font-size: 14px; margin-top: 2px;">${order.vehicle.licensePlate || '--'}</div>
					</div>`,
					{
						offset: new BMapGL.Size(-50, -50),
					},
				)

				label.setStyle({
					color: 'white',
					fontSize: '10px',
					background: 'rgba(0, 0, 0, 0.4)',
					border: 'none',
					borderRadius: '5px',
					padding: '2px 6px',
				})
				marker.setLabel(label)

				orderMarkers.set(orderId, marker)
			}
		}
	})
}

// 显示运单详情
const showOrderDetails = (orderId: string) => {
	orderStore.selectOrder(orderId)
	selectedOrderId.value = orderId
}

// 关闭运单详情
const closeOrderDetails = () => {
	orderStore.unselectOrder()
	selectedOrderId.value = null
}

// 获取铅封状态样式
const getSealStatusClass = (status: number): string => {
	switch (status) {
		case 1: // 开
			return 'bg-green-500/20 text-green-400 border border-green-500/30'
		case 2: // 关
			return 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
		case 3: // 异常
			return 'bg-red-500/20 text-red-400 border border-red-500/30'
		default:
			return 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
	}
}

// 左上角的图层显示
const toggleLayer = (layerName: keyof typeof layerVisible) => {
	console.log(`切换图层: ${layerName}`, layerVisible[layerName])

	switch (layerName) {
		case 'powerPlant':
			if (layerVisible.powerPlant) {
				addPowerPlantMarkers()
			} else {
				powerPlantMarkers.forEach((marker) => map.removeOverlay(marker))
				powerPlantMarkers = []
			}
			break
		case 'coalMine':
			if (layerVisible.coalMine) {
				addCoalMineMarkers()
			} else {
				coalMineMarkers.forEach((marker) => map.removeOverlay(marker))
				coalMineMarkers = []
			}
			break
		case 'vehicles':
			if (layerVisible.vehicles) {
				updateOrderMarkers(orderStore.orderList)
				// 显示示例车辆
				normalMarkers.forEach((marker) => map.addOverlay(marker))
				warningMarkers.forEach((marker) => map.addOverlay(marker))
				dangerMarkers.forEach((marker) => map.addOverlay(marker))
			} else {
				orderMarkers.forEach((marker) => map.removeOverlay(marker))
				orderMarkers.clear()
				// 隐藏示例车辆
				normalMarkers.forEach((marker) => map.removeOverlay(marker))
				warningMarkers.forEach((marker) => map.removeOverlay(marker))
				dangerMarkers.forEach((marker) => map.removeOverlay(marker))
			}
			break
		case 'roadNetwork':
			if (layerVisible.roadNetwork) {
				trackPolylines.forEach((polyline) => map.addOverlay(polyline))
			} else {
				trackPolylines.forEach((polyline) => map.removeOverlay(polyline))
			}
			break
	}
}

// 组件挂载时初始化地图
onMounted(async () => {
	try {
		console.log('初始化地图...')
		// 加载百度地图API
		await loadBaiduMapAPI()

		// 首次加载地点与电厂列表，保障定位可用
		await locationStore.getList()
		await plantStore.getList()

		console.log('地图初始化成功！')
		await initMap()
	} catch (error) {
		console.error('加载百度地图API失败:', error)
	}
})

// 组件卸载时清理资源
onBeforeUnmount(() => {
	orderStore.closeWebSocket()

	if (map) {
		// 清理所有标记
		orderMarkers.forEach((marker) => map.removeOverlay(marker))
		powerPlantMarkers.forEach((marker) => map.removeOverlay(marker))
		coalMineMarkers.forEach((marker) => map.removeOverlay(marker))
		normalMarkers.forEach((marker) => map.removeOverlay(marker))
		warningMarkers.forEach((marker) => map.removeOverlay(marker))
		dangerMarkers.forEach((marker) => map.removeOverlay(marker))
		trackPolylines.forEach((polyline) => map.removeOverlay(polyline))

		// 清理当前轨迹线
		if (currentTrackPolyline) {
			map.removeOverlay(currentTrackPolyline)
			currentTrackPolyline = null
		}

		map = null
		BMapGL = null
	}
})
</script>

<template>
	<section class="relative flex-1">
		<!-- 地图容器 -->
		<div id="map" class="h-full w-full overflow-hidden rounded-lg"></div>

		<!-- 地图图层控制 -->
		<div class="glass absolute left-3 top-0 z-30 rounded-lg p-2">
			<div class="flex flex-col space-y-2">
				<div class="flex items-center justify-between">
					<span class="mr-3 text-xs">路网图层</span>
					<label class="relative inline-flex cursor-pointer items-center">
						<input v-model="layerVisible.roadNetwork" type="checkbox" class="peer sr-only" @change="toggleLayer('roadNetwork')" />
						<div
							class="peer h-4 w-8 rounded-full bg-gray-700 after:absolute after:left-[2px] after:top-[2px] after:h-3 after:w-3 after:border after:border-gray-300 after:rounded-full after:bg-white peer-checked:bg-success peer-focus:outline-none after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white"
						></div>
					</label>
				</div>
				<div class="flex items-center justify-between">
					<span class="mr-3 text-xs">电厂标记</span>
					<label class="relative inline-flex cursor-pointer items-center">
						<input v-model="layerVisible.powerPlant" type="checkbox" class="peer sr-only" @change="toggleLayer('powerPlant')" />
						<div
							class="peer h-4 w-8 rounded-full bg-gray-700 after:absolute after:left-[2px] after:top-[2px] after:h-3 after:w-3 after:border after:border-gray-300 after:rounded-full after:bg-white peer-checked:bg-success peer-focus:outline-none after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white"
						></div>
					</label>
				</div>
				<div class="flex items-center justify-between">
					<span class="mr-3 text-xs">煤矿标记</span>
					<label class="relative inline-flex cursor-pointer items-center">
						<input v-model="layerVisible.coalMine" type="checkbox" class="peer sr-only" @change="toggleLayer('coalMine')" />
						<div
							class="peer h-4 w-8 rounded-full bg-gray-700 after:absolute after:left-[2px] after:top-[2px] after:h-3 after:w-3 after:border after:border-gray-300 after:rounded-full after:bg-white peer-checked:bg-success peer-focus:outline-none after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white"
						></div>
					</label>
				</div>
				<div class="flex items-center justify-between">
					<span class="mr-3 text-xs">运单 ({{ orderStore.onlineCount }}/{{ orderStore.totalCount }})</span>
					<label class="relative inline-flex cursor-pointer items-center">
						<input v-model="layerVisible.vehicles" type="checkbox" class="peer sr-only" @change="toggleLayer('vehicles')" />
						<div
							class="peer h-4 w-8 rounded-full bg-gray-700 after:absolute after:left-[2px] after:top-[2px] after:h-3 after:w-3 after:border after:border-gray-300 after:rounded-full after:bg-white peer-checked:bg-success peer-focus:outline-none after:transition-all after:content-[''] peer-checked:after:translate-x-full peer-checked:after:border-white"
						></div>
					</label>
				</div>
			</div>
		</div>

		<!-- 运单弹窗信息 -->
		<div v-if="orderStore.selectedOrder" class="glass vehicle-info absolute bottom-3 right-3 z-30 w-64 rounded-lg p-3">
			<div class="mb-2 flex items-center justify-between">
				<h3 class="font-bold text-sm">运单详情</h3>
				<div class="flex pb-2 space-x-1">
					<button v-if="orderStore.selectedOrder.alertNum > 0" class="hover:bg-secondary-light rounded bg-error px-1.5 py-0.5 text-sm mr-2">告警 {{ orderStore.selectedOrder.alertNum }} 个</button>

					<!-- <button class="hover:bg-secondary-light rounded bg-success px-1.5 py-0.5 text-sm">轨迹</button> -->
					<!--					<button class="hover:bg-secondary-light rounded bg-success px-1.5 py-0.5 text-sm">联系</button>-->
					<button class="rounded bg-gray-600 px-2 py-0.5 text-sm hover:bg-red-600" @click="closeOrderDetails">×</button>
				</div>
			</div>

			<div class="flex items-center text-white text-sm mb-1">
				<div class="mr-2 h-2 w-2 flex-shrink-0 rounded-full bg-green-500"></div>
				<span class="flex-1 truncate">{{ orderStore.selectedOrder.departure && orderStore.selectedOrder.departure.name }}</span>
				<span class="mx-2 text-white/60">→</span>
				<div class="mr-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
				<span class="flex-1 truncate">{{ orderStore.selectedOrder.destination && orderStore.selectedOrder.destination.name }}</span>
			</div>

			<div class="text-sm space-y-1">
				<div class="flex justify-between">
					<span class="text-gray-300">电厂电话：</span>
					<span class="font-medium">{{ orderStore.selectedOrder?.dept?.phone }}</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-300">运单号：</span>
					<span class="font-medium">{{ orderStore.selectedOrder.orderNo }}</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-300">车牌号：</span>
					<span class="font-medium">{{ orderStore.selectedOrder.vehicle.licensePlate || '--' }}</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-300">司机：</span>
					<span class="font-medium">{{ orderStore.selectedOrder?.driverName || '--' }}</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-300">联系方式：</span>
					<span class="font-medium">{{ orderStore.selectedOrder?.driverPhone || '--' }}</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-300">当前位置：</span>
					<span class="font-medium">{{ orderStore.selectedOrder.currentPosition?.address || '--' }}</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-300">当前GPS：</span>
					<span class="font-medium">
						{{ (orderStore.selectedOrder.currentPosition?.lng || '') + ',' + (orderStore.selectedOrder.currentPosition?.lat || '') }}
					</span>
				</div>
				<!-- <div class="flex justify-between">
					<span class="text-gray-300">当前速度：</span>
					<span class="font-medium">{{ orderStore.selectedOrder.currentPosition?.speed || 0 }} km/h</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-300">行驶方向：</span>
					<span class="font-medium">{{ orderStore.getDirectionText(orderStore.selectedOrder.currentPosition?.direction) }}</span>
				</div> -->
				<div class="flex justify-between">
					<span class="text-gray-300">运单状态：</span>
					<span class="font-medium" :style="{ color: orderStore.getOrderStatusColor(orderStore.selectedOrder as any) }">
						{{ orderStore.formatOrderStatus(orderStore.selectedOrder.status) }}
					</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-300">在线状态：</span>
					<span class="font-medium" :class="orderStore.selectedOrder.vehicleOnlineStatus === 'online' ? 'text-green-400' : 'text-red-400'">
						{{ orderStore.formatVehicleOnlineStatus(orderStore.selectedOrder.vehicleOnlineStatus) }}
					</span>
				</div>
				<div v-if="orderStore.selectedOrder.offlineTime" class="flex justify-between">
					<span class="text-gray-300">离线时间：</span>
					<span class="text-red-400 font-medium">{{ new Date(orderStore.selectedOrder.offlineTime).toLocaleTimeString() }}</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-300">发车时间：</span>
					<span class="font-medium">{{ orderStore.formatEstimateArriveTime(orderStore.selectedOrder.deliveryTime) }}</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-300">预计到达：</span>
					<span class="font-medium">{{ orderStore.formatEstimateArriveTime(orderStore.selectedOrder.estimateArriveTime) }}</span>
				</div>
			</div>

			<!-- 铅封状态 -->
			<div v-if="orderStore.selectedOrder.sealDevices && orderStore.selectedOrder.sealDevices.length > 0" class="mt-3 border-gray-600 pt-3 border-t">
				<p class="mb-2 text-gray-400 text-sm">铅封状态：</p>
				<div class="space-y-1">
					<div v-for="seal in orderStore.selectedOrder.sealDevices" :key="seal.deviceNo" class="flex items-center justify-between text-sm">
						<span class="text-gray-300">{{ seal.deviceNo }}</span>
						<span class="rounded px-2 py-0.5 font-medium text-xs" :class="getSealStatusClass(seal.status)">
							{{ seal.statusName }}
						</span>
					</div>
				</div>
			</div>
			<div class="mt-3 border-gray-600 pt-3 border-t">
				<div class="flex items-center justify-between">
					<div>
						<p class="text-gray-400 text-sm">行程信息：</p>
						<p class="text-sm">已行驶 {{ orderStore.selectedOrder.runDistance || 0 }} km</p>
					</div>
					<div class="text-right">
						<p class="text-gray-400 text-sm">剩余距离：</p>
						<p class="text-sm">{{ orderStore.selectedOrder.remainDistance || 0 }} km</p>
					</div>
				</div>
				<div v-if="orderStore.selectedOrder.mileage" class="mt-2">
					<p class="text-gray-400 text-sm">总里程：{{ orderStore.selectedOrder.mileage }} km</p>
				</div>
			</div>
		</div>
	</section>
</template>

<style scoped lang="scss">
.glass {
	background: rgba(255, 255, 255, 0.05);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.1);
	box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}
</style>
