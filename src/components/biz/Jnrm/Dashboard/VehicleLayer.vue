<script setup lang="ts">
import { useTransportOrderStore, type TransportOrder } from '~/stores/useTransportOrderStore'

interface Props {
  map: any // 百度地图实例
  BMapGL: any // 百度地图GL类
  visible: boolean // 车辆图层是否可见
  selectedDeptId?: number // 选中的电厂ID，用于过滤运单
}

interface Emits {
  (e: 'vehicle-click', orderId: string): void // 车辆点击事件
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 使用运单store
const orderStore = useTransportOrderStore()

// 运单标记映射表
const orderMarkers = new Map<string, any>()

// 当前显示的轨迹线
let currentTrackPolyline: any = null

/**
 * 创建车辆图标
 * @param type 车辆状态类型
 * @returns 百度地图图标对象
 */
const createCarIcon = (type: string) => {
  return new props.BMapGL.Icon('/truck.png', new props.BMapGL.Size(type === 'normal' ? 40 : 50, type === 'normal' ? 40 : 50), {
    anchor: new props.BMapGL.Size(type === 'normal' ? 20 : 25, type === 'normal' ? 20 : 25),
  })
}

/**
 * 更新运单标记
 * @param orderList 运单列表
 */
const updateOrderMarkers = (orderList: TransportOrder[]) => {
  if (!props.map || !props.visible) return

  // 移除不存在的运单标记
  for (const [orderId, marker] of orderMarkers.entries()) {
    const orderExists = orderList.some((order) => order.id === orderId)
    if (!orderExists) {
      props.map.removeOverlay(marker) // 地图删掉
      orderMarkers.delete(orderId) // 数据删掉
    }
  }

  // 添加或更新运单标记
  orderList.forEach((order) => {
    if (order.currentPosition) {
      const { lng, lat } = order.currentPosition
      const orderId = order.id

      // 获取运单状态颜色
      const color = orderStore.getOrderStatusColor(order)

      // 创建或更新标记
      if (orderMarkers.has(orderId)) {
        // 更新现有标记位置
        const marker = orderMarkers.get(orderId)
        const newPoint = new props.BMapGL.Point(lng, lat)
        marker.setPosition(newPoint)
      } else {
        // 创建新标记
        const point = new props.BMapGL.Point(lng, lat)
        const icon = createCarIcon(color)
        const marker = new props.BMapGL.Marker(point, { icon })

        props.map.addOverlay(marker)

        // 添加点击事件
        marker.addEventListener('click', () => {
          emit('vehicle-click', orderId)
        })

        // 添加标签
        const label = new props.BMapGL.Label(
          `<div style="text-align: center;">
            <div style="font-size: 16px;">${order.orderNo}</div>
            <div style="font-size: 14px; margin-top: 2px;">${order.vehicle.licensePlate || '--'}</div>
          </div>`,
          {
            offset: new props.BMapGL.Size(-50, -50),
          },
        )

        label.setStyle({
          color: 'white',
          fontSize: '10px',
          background: 'rgba(0, 0, 0, 0.4)',
          border: 'none',
          borderRadius: '5px',
          padding: '2px 6px',
        })
        marker.setLabel(label)

        orderMarkers.set(orderId, marker)
      }
    }
  })
}

/**
 * 绘制车辆轨迹
 * @param order 选中的运单
 */
const drawVehicleTrack = (order: TransportOrder) => {
  if (!props.map) return

  // 清除之前的轨迹线
  if (currentTrackPolyline) {
    props.map.removeOverlay(currentTrackPolyline)
    currentTrackPolyline = null
  }

  // 检查边界条件：historyPositions为空或不存在
  if (!order.historyPositions || order.historyPositions.length === 0) {
    console.log('运单轨迹数据为空，无法绘制轨迹')
    return
  }

  // 将历史位置转换为百度地图点数组
  const points = order.historyPositions
    .filter((pos: any) => pos.lng && pos.lat) // 过滤无效坐标
    .map((pos: any) => new props.BMapGL.Point(pos.lng, pos.lat))

  // 如果有当前位置，也加入轨迹
  if (order.currentPosition && order.currentPosition.lng && order.currentPosition.lat) {
    points.push(new props.BMapGL.Point(order.currentPosition.lng, order.currentPosition.lat))
  }

  // 检查是否有足够的点来绘制轨迹
  if (points.length < 2) {
    console.log('轨迹点数量不足，无法绘制轨迹')
    return
  }

  // 创建轨迹线
  currentTrackPolyline = new props.BMapGL.Polyline(points, {
    strokeTexture: {
      url: 'https://mapopen-pub-jsapigl.bj.bcebos.com/svgmodel/Icon_road_blue_arrow.png',
      width: 16,
      height: 64,
    },
    strokeWeight: 8,
    strokeOpacity: 0.8,
  })

  // 添加轨迹线到地图
  props.map.addOverlay(currentTrackPolyline)

  console.log(`已绘制运单 ${order.orderNo} 的轨迹，包含 ${points.length} 个点`)
}

/**
 * 清除所有车辆标记
 */
const clearAllMarkers = () => {
  orderMarkers.forEach((marker) => {
    if (props.map) {
      props.map.removeOverlay(marker)
    }
  })
  orderMarkers.clear()
}

/**
 * 清除轨迹线
 */
const clearTrack = () => {
  if (currentTrackPolyline && props.map) {
    props.map.removeOverlay(currentTrackPolyline)
    currentTrackPolyline = null
  }
}

/**
 * 根据电厂选择过滤运单并刷新标记
 */
const refreshOrdersOnMap = () => {
  const base = orderStore.orderList
  const filtered = props.selectedDeptId ? base.filter((o: any) => o.deptId === props.selectedDeptId) : base
  updateOrderMarkers(filtered)
}

// 监听运单列表变化，更新地图标记
watch(
  () => orderStore.orderList,
  () => {
    if (props.map) {
      refreshOrdersOnMap()
    }
  },
  { deep: true },
)

// 监听选中运单变化，自动绘制轨迹
watch(
  () => orderStore.selectedOrder,
  (newOrder) => {
    if (newOrder && props.map) {
      drawVehicleTrack(newOrder)
    } else {
      clearTrack()
    }
  },
)

// 监听图层可见性变化
watch(
  () => props.visible,
  (visible) => {
    if (!props.map) return
    
    if (visible) {
      refreshOrdersOnMap()
    } else {
      clearAllMarkers()
      clearTrack()
    }
  },
)

// 监听电厂选择变化
watch(
  () => props.selectedDeptId,
  () => {
    if (props.map) {
      clearAllMarkers()
      refreshOrdersOnMap()
    }
  },
)

// 暴露方法给父组件
defineExpose({
  updateOrderMarkers,
  drawVehicleTrack,
  clearAllMarkers,
  clearTrack,
  refreshOrdersOnMap,
})

// 组件卸载时清理资源
onBeforeUnmount(() => {
  clearAllMarkers()
  clearTrack()
})
</script>

<template>
  <!-- 车辆图层组件不需要渲染任何DOM，只负责地图上的车辆标记管理 -->
</template>
