<script setup lang="ts">
import { useTransportOrderStore, type TransportOrder } from '~/stores/useTransportOrderStore'

interface Props {
  order: TransportOrder | null // 选中的运单
  visible: boolean // 弹窗是否可见
}

interface Emits {
  (e: 'close'): void // 关闭弹窗事件
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 使用运单store
const orderStore = useTransportOrderStore()

/**
 * 获取铅封状态样式
 * @param status 铅封状态
 * @returns CSS类名
 */
const getSealStatusClass = (status: number): string => {
  switch (status) {
    case 1: // 开
      return 'bg-green-500/20 text-green-400 border border-green-500/30'
    case 2: // 关
      return 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
    case 3: // 异常
      return 'bg-red-500/20 text-red-400 border border-red-500/30'
    default:
      return 'bg-gray-500/20 text-gray-400 border border-gray-500/30'
  }
}

/**
 * 关闭弹窗
 */
const handleClose = () => {
  emit('close')
}
</script>

<template>
  <!-- 运单弹窗信息 -->
  <div v-if="visible && order" class="glass vehicle-info absolute bottom-3 right-3 z-30 w-64 rounded-lg p-3">
    <div class="mb-2 flex items-center justify-between">
      <h3 class="font-bold text-sm">运单详情</h3>
      <div class="flex pb-2 space-x-1">
        <button v-if="order.alertNum > 0" class="hover:bg-secondary-light rounded bg-error px-1.5 py-0.5 text-sm mr-2">
          告警 {{ order.alertNum }} 个
        </button>
        <button class="rounded bg-gray-600 px-2 py-0.5 text-sm hover:bg-red-600" @click="handleClose">×</button>
      </div>
    </div>

    <!-- 出发地和目的地 -->
    <div class="flex items-center text-white text-sm mb-1">
      <div class="mr-2 h-2 w-2 flex-shrink-0 rounded-full bg-green-500"></div>
      <span class="flex-1 truncate">{{ order.departure && order.departure.name }}</span>
      <span class="mx-2 text-white/60">→</span>
      <div class="mr-2 h-2 w-2 flex-shrink-0 rounded-full bg-blue-500"></div>
      <span class="flex-1 truncate">{{ order.destination && order.destination.name }}</span>
    </div>

    <!-- 基本信息 -->
    <div class="text-sm space-y-1">
      <div class="flex justify-between">
        <span class="text-gray-300">电厂电话：</span>
        <span class="font-medium">{{ order?.dept?.phone }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-300">运单号：</span>
        <span class="font-medium">{{ order.orderNo }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-300">车牌号：</span>
        <span class="font-medium">{{ order.vehicle.licensePlate || '--' }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-300">司机：</span>
        <span class="font-medium">{{ order?.driverName || '--' }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-300">联系方式：</span>
        <span class="font-medium">{{ order?.driverPhone || '--' }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-300">当前位置：</span>
        <span class="font-medium">{{ order.currentPosition?.address || '--' }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-300">当前GPS：</span>
        <span class="font-medium">
          {{ (order.currentPosition?.lng || '') + ',' + (order.currentPosition?.lat || '') }}
        </span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-300">运单状态：</span>
        <span class="font-medium" :style="{ color: orderStore.getOrderStatusColor(order as any) }">
          {{ orderStore.formatOrderStatus(order.status) }}
        </span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-300">在线状态：</span>
        <span class="font-medium" :class="order.vehicleOnlineStatus === 'online' ? 'text-green-400' : 'text-red-400'">
          {{ orderStore.formatVehicleOnlineStatus(order.vehicleOnlineStatus) }}
        </span>
      </div>
      <div v-if="order.offlineTime" class="flex justify-between">
        <span class="text-gray-300">离线时间：</span>
        <span class="text-red-400 font-medium">{{ new Date(order.offlineTime).toLocaleTimeString() }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-300">发车时间：</span>
        <span class="font-medium">{{ orderStore.formatEstimateArriveTime(order.deliveryTime) }}</span>
      </div>
      <div class="flex justify-between">
        <span class="text-gray-300">预计到达：</span>
        <span class="font-medium">{{ orderStore.formatEstimateArriveTime(order.estimateArriveTime) }}</span>
      </div>
    </div>

    <!-- 铅封状态 -->
    <div v-if="order.sealDevices && order.sealDevices.length > 0" class="mt-3 border-gray-600 pt-3 border-t">
      <p class="mb-2 text-gray-400 text-sm">铅封状态：</p>
      <div class="space-y-1">
        <div v-for="seal in order.sealDevices" :key="seal.deviceNo" class="flex items-center justify-between text-sm">
          <span class="text-gray-300">{{ seal.deviceNo }}</span>
          <span class="rounded px-2 py-0.5 font-medium text-xs" :class="getSealStatusClass(seal.status)">
            {{ seal.statusName }}
          </span>
        </div>
      </div>
    </div>

    <!-- 行程信息 -->
    <div class="mt-3 border-gray-600 pt-3 border-t">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-gray-400 text-sm">行程信息：</p>
          <p class="text-sm">已行驶 {{ order.runDistance || 0 }} km</p>
        </div>
        <div class="text-right">
          <p class="text-gray-400 text-sm">剩余距离：</p>
          <p class="text-sm">{{ order.remainDistance || 0 }} km</p>
        </div>
      </div>
      <div v-if="order.mileage" class="mt-2">
        <p class="text-gray-400 text-sm">总里程：{{ order.mileage }} km</p>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}
</style>
